const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const ffmpeg = require('fluent-ffmpeg');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        const uniqueName = `${uuidv4()}-${file.originalname}`;
        cb(null, uniqueName);
    }
});

const upload = multer({
    storage,
    limits: {
        fileSize: 500 * 1024 * 1024 // 500MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
        const ext = path.extname(file.originalname).toLowerCase();
        if (allowedTypes.includes(ext)) {
            cb(null, true);
        } else {
            cb(new Error(`Unsupported file type: ${ext}`));
        }
    }
});

// Upload videos endpoint
router.post('/upload-videos', upload.array('videos', 50), async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({ error: 'No video files uploaded' });
        }

        const uploadedFiles = req.files.map(file => ({
            id: uuidv4(),
            originalName: file.originalname,
            filename: file.filename,
            path: file.path,
            size: file.size
        }));

        res.json({
            success: true,
            message: `${uploadedFiles.length} videos uploaded successfully`,
            files: uploadedFiles
        });
    } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({ error: 'Failed to upload videos' });
    }
});

// Get video duration using ffprobe
const getVideoDuration = (filePath) => {
    return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(filePath, (err, metadata) => {
            if (err) {
                reject(err);
            } else {
                resolve(metadata.format.duration);
            }
        });
    });
};

// Generate random clips from videos based on clip count instead of duration
const selectRandomClips = async (files, clipCount) => {
    const clips = [];
    const absoluteMinDuration = 0.5; // Absolute minimum - even very short clips are acceptable
    const preferredMinDuration = 1.0; // Preferred minimum, but not required
    const maxClipDuration = 15; // Maximum duration for any clip
    const maxAttempts = files.length * 3; // Reasonable attempts
    let attempts = 0;
    let totalDuration = 0; // Initialize totalDuration variable

    console.log(`Selecting ${clipCount} clips from ${files.length} files`);
    console.log(`Using clip durations: min=${absoluteMinDuration}s, preferred=${preferredMinDuration}s, max=${maxClipDuration}s`);

    // Create a deep copy of files array for randomization
    let availableFiles = [...files];

    // Properly shuffle the array using Fisher-Yates algorithm for true randomness
    for (let i = availableFiles.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [availableFiles[i], availableFiles[j]] = [availableFiles[j], availableFiles[i]];
    }

    // Track used files to prevent duplicates in a single generation
    const usedFileIds = new Set();

    // First pass: collect the requested number of clips
    while (clips.length < clipCount && attempts < maxAttempts && availableFiles.length > 0) {
        attempts++;

        // Get next available file, if we've used all files and still need more clips, re-shuffle and start again
        let fileIndex = attempts % availableFiles.length;
        const randomFile = availableFiles[fileIndex];

        // Mark this file as used to prevent duplicates
        usedFileIds.add(randomFile.id);

        try {
            const videoDuration = await getVideoDuration(randomFile.path);
            console.log(`File ${randomFile.originalName}: duration ${videoDuration}s`);

            // Skip only extremely short videos (less than 0.5s)
            if (videoDuration < absoluteMinDuration) {
                console.log(`Skipping ${randomFile.originalName}: too short (${videoDuration}s)`);
                continue;
            }

            // For very short videos, use the entire duration
            let clipDuration;
            let startTime = 0;

            if (videoDuration <= preferredMinDuration) {
                // Use the entire short video
                clipDuration = videoDuration;
                startTime = 0;
                console.log(`Using entire short video: ${randomFile.originalName} (${clipDuration}s)`);
            } else {
                // For longer videos, extract a random clip
                const maxPossibleClip = Math.min(maxClipDuration, videoDuration - 0.1);
                const minForThisClip = Math.min(preferredMinDuration, maxPossibleClip);

                clipDuration = Math.random() * (maxPossibleClip - minForThisClip) + minForThisClip;
                const maxStartTime = videoDuration - clipDuration;
                startTime = Math.random() * maxStartTime;
            }

            const clip = {
                file: randomFile,
                startTime: Math.max(0, Math.floor(startTime * 10) / 10), // Round to 1 decimal
                duration: Math.max(absoluteMinDuration, Math.floor(clipDuration * 10) / 10) // Round to 1 decimal
            };

            clips.push(clip);
            totalDuration += clip.duration; // Track total duration

            console.log(`Added clip ${clips.length}: ${randomFile.originalName} (${clip.startTime}s-${clip.startTime + clip.duration}s, duration: ${clip.duration}s)`);

            // Remove this file from available files to prevent duplicates
            availableFiles = availableFiles.filter(file => file.id !== randomFile.id);

            // If we've used all files but still need more clips, reset available files
            // but exclude files we've already used in this generation
            if (availableFiles.length === 0 && clips.length < clipCount) {
                availableFiles = files.filter(file => !usedFileIds.has(file.id));
                // If all files have been used once and we still need more, we'll have to allow reuse
                if (availableFiles.length === 0) {
                    console.log('All files have been used once, allowing reuse for remaining clips');
                    availableFiles = [...files];
                    // Re-shuffle available files
                    for (let i = availableFiles.length - 1; i > 0; i--) {
                        const j = Math.floor(Math.random() * (i + 1));
                        [availableFiles[i], availableFiles[j]] = [availableFiles[j], availableFiles[i]];
                    }
                }
            }

            // Stop if we've reached the requested number of clips
            if (clips.length >= clipCount) {
                console.log(`Target clip count reached: ${clips.length} clips`);
                break;
            }

        } catch (error) {
            console.error(`Error processing ${randomFile.originalName}:`, error);
        }
    }

    // If we still don't have enough clips, do a second pass with even more relaxed requirements
    if (clips.length === 0 && files.length > 0) {
        console.log(`No clips found in first pass, trying with any available videos...`);

        // Reset available files but maintain true randomness
        availableFiles = [...files];
        // Fisher-Yates shuffle
        for (let i = availableFiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [availableFiles[i], availableFiles[j]] = [availableFiles[j], availableFiles[i]];
        }

        for (const file of availableFiles.slice(0, Math.min(10, availableFiles.length))) {
            // Skip if we've already used this file
            if (usedFileIds.has(file.id)) continue;

            try {
                const videoDuration = await getVideoDuration(file.path);
                if (videoDuration >= absoluteMinDuration) {
                    const clip = {
                        file: file,
                        startTime: 0,
                        duration: Math.min(videoDuration, maxClipDuration)
                    };
                    clips.push(clip);
                    totalDuration += clip.duration;
                    usedFileIds.add(file.id);
                    console.log(`Fallback clip added: ${file.originalName} (${clip.duration}s)`);

                    if (clips.length >= 5) break; // At least get a few clips
                }
            } catch (error) {
                console.error(`Error in fallback processing ${file.originalName}:`, error);
            }
        }
    }

    console.log(`Final selection: ${clips.length} clips with total duration ${totalDuration.toFixed(1)}s`);
    return clips;
};

// Extract clip using FFmpeg
const extractClip = (clip, outputPath) => {
    return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
            reject(new Error(`Clip extraction timeout for ${clip.file.originalName}`));
        }, 90000); // Increased timeout

        console.log(`Extracting clip from ${clip.file.originalName}: ${clip.startTime}s for ${clip.duration}s`);

        ffmpeg(clip.file.path)
            .seekInput(clip.startTime)
            .duration(clip.duration)
            .outputOptions([
                '-c:v libx264',
                '-c:a aac',
                '-preset fast', // Changed from ultrafast for better compatibility
                '-crf 23',
                '-pix_fmt yuv420p', // Ensure compatible pixel format
                '-r 30', // Set consistent frame rate
                '-ar 44100', // Set consistent audio sample rate
                '-ac 2' // Ensure stereo audio
            ])
            .on('start', (commandLine) => {
                console.log('FFmpeg command:', commandLine);
            })
            .on('progress', (progress) => {
                console.log(`Clip extraction progress: ${progress.percent}%`);
            })
            .on('end', async () => {
                clearTimeout(timeout);
                try {
                    const stats = await fs.stat(outputPath);
                    if (stats.size === 0) {
                        throw new Error('Generated clip is empty');
                    }
                    console.log(`Clip extracted successfully: ${outputPath} (${stats.size} bytes)`);
                    resolve();
                } catch (error) {
                    reject(error);
                }
            })
            .on('error', (error) => {
                clearTimeout(timeout);
                console.error(`FFmpeg error for ${clip.file.originalName}:`, error);
                reject(error);
            })
            .save(outputPath);
    });
};

// Concatenate clips using FFmpeg
const concatenateClips = (clipPaths, outputPath) => {
    return new Promise(async (resolve, reject) => {
        try {
            if (clipPaths.length === 0) {
                throw new Error('No clips to concatenate');
            }

            if (clipPaths.length === 1) {
                // If only one clip, just copy it
                await fs.copyFile(clipPaths[0], outputPath);
                console.log(`Single clip copied to: ${outputPath}`);
                resolve();
                return;
            }

            const concatListPath = path.join('temp', `concat-${uuidv4()}.txt`);

            // Verify all clip files exist before creating concat list
            for (const clipPath of clipPaths) {
                try {
                    await fs.access(clipPath);
                } catch (error) {
                    throw new Error(`Clip file not found: ${clipPath}`);
                }
            }

            // Create concat list with proper escaping for Windows paths
            const concatContent = clipPaths.map(clipPath => {
                const absolutePath = path.resolve(clipPath);
                // Escape single quotes and backslashes for concat demuxer
                const escapedPath = absolutePath.replace(/'/g, "'\\''").replace(/\\/g, '/');
                return `file '${escapedPath}'`;
            }).join('\n');

            console.log('Concat list content:', concatContent);
            await fs.writeFile(concatListPath, concatContent);

            const timeout = setTimeout(() => {
                reject(new Error('Video concatenation timeout'));
            }, 180000); // Increased timeout

            console.log(`Concatenating ${clipPaths.length} clips to: ${outputPath}`);

            ffmpeg()
                .input(concatListPath)
                .inputOptions(['-f', 'concat', '-safe', '0'])
                .outputOptions([
                    '-c:v libx264', // Re-encode video for compatibility
                    '-c:a aac', // Re-encode audio for compatibility
                    '-preset fast',
                    '-crf 23',
                    '-pix_fmt yuv420p',
                    '-r 30',
                    '-ar 44100',
                    '-ac 2',
                    '-avoid_negative_ts', 'make_zero'
                ])
                .on('start', (commandLine) => {
                    console.log('FFmpeg concat command:', commandLine);
                })
                .on('progress', (progress) => {
                    console.log(`Concatenation progress: ${progress.percent}%`);
                })
                .on('end', async () => {
                    clearTimeout(timeout);
                    try {
                        await fs.unlink(concatListPath);
                        const stats = await fs.stat(outputPath);
                        console.log(`Concatenation complete: ${outputPath} (${stats.size} bytes)`);
                        resolve();
                    } catch (error) {
                        console.error('Error cleaning up concat file:', error);
                        resolve(); // Don't fail the whole process
                    }
                })
                .on('error', async (error) => {
                    clearTimeout(timeout);
                    console.error('FFmpeg concatenation error:', error);
                    try {
                        await fs.unlink(concatListPath);
                    } catch (cleanupError) {
                        console.error('Error cleaning up concat file:', cleanupError);
                    }
                    reject(error);
                })
                .save(outputPath);
        } catch (error) {
            console.error('Concatenation setup error:', error);
            reject(error);
        }
    });
};

// Generate a single video
const generateSingleVideo = async (files, clipsPerVideo, videoNumber, progressCallback, splitterFile = null) => {
    const videoId = uuidv4();
    const tempClips = [];
    const finalClips = [];
    const useSplitter = splitterFile !== null;

    console.log(`Starting generation of video ${videoNumber} with ${clipsPerVideo} clips${useSplitter ? ' and splitter video' : ''}`);

    try {
        progressCallback(10, `Selecting clips for video ${videoNumber}...`);

        const clips = await selectRandomClips(files, clipsPerVideo);
        if (clips.length === 0) {
            throw new Error('No suitable clips found. Please ensure your videos are at least 0.5 seconds long.');
        }

        console.log(`Selected ${clips.length} clips for video ${videoNumber}`);
        progressCallback(20, `Selected ${clips.length} clips, starting extraction...`);

        // Extract splitter clip if provided
        let splitterClipPath = null;
        if (useSplitter) {
            const splitterTemp = path.join('temp', `splitter-${videoId}.mp4`);
            console.log(`Extracting splitter video: ${splitterFile.originalName}`);

            const splitterClip = {
                file: splitterFile,
                startTime: 0,
                duration: await getVideoDuration(splitterFile.path)
            };

            await extractClip(splitterClip, splitterTemp);
            splitterClipPath = splitterTemp;
            console.log(`Extracted splitter video: ${splitterTemp}`);
        }

        // Extract clips
        for (let i = 0; i < clips.length; i++) {
            const clip = clips[i];
            const tempClipPath = path.join('temp', `clip-${videoId}-${i}.mp4`);

            progressCallback(20 + (i / clips.length) * 50, `Extracting clip ${i + 1}/${clips.length} from ${clip.file.originalName}...`);

            await extractClip(clip, tempClipPath);
            tempClips.push(tempClipPath);
            finalClips.push(tempClipPath);

            // Add splitter after each clip except the last one
            if (useSplitter && i < clips.length - 1) {
                finalClips.push(splitterClipPath);
            }

            console.log(`Extracted clip ${i + 1}/${clips.length}: ${tempClipPath}`);
        }

        const totalClips = finalClips.length;
        progressCallback(70, `Combining ${totalClips} clips into final video...`);

        // Concatenate clips
        const outputFilename = `remix-${videoId}.mp4`;
        const outputPath = path.join('output', outputFilename);

        console.log(`Starting concatenation of ${finalClips.length} clips${useSplitter ? ' (includes splitter videos)' : ''}`);
        await concatenateClips(finalClips, outputPath);

        progressCallback(90, 'Finalizing video...');

        // Verify output file
        const stats = await fs.stat(outputPath);
        if (stats.size === 0) {
            throw new Error('Generated video is empty');
        }

        console.log(`Video ${videoNumber} generated successfully: ${outputPath} (${stats.size} bytes, ${clips.length} clips)`);
        progressCallback(100, 'Video ready!');

        return {
            id: videoId,
            filename: outputFilename,
            path: outputPath,
            size: stats.size,
            clipCount: clips.length,
            duration: clips.reduce((sum, clip) => sum + clip.duration, 0)
        };

    } catch (error) {
        console.error(`Error generating video ${videoNumber}:`, error);
        throw new Error(`Failed to generate video ${videoNumber}: ${error.message}`);
    } finally {
        // Cleanup temporary clips
        console.log(`Cleaning up ${tempClips.length} temporary clips for video ${videoNumber}`);
        for (const clipPath of tempClips) {
            try {
                await fs.unlink(clipPath);
                console.log(`Cleaned up: ${clipPath}`);
            } catch (error) {
                console.error(`Error cleaning up ${clipPath}:`, error);
            }
        }
    }
};

// Generate videos endpoint with Server-Sent Events
router.post('/generate-videos', async (req, res) => {
    try {
        const { files, clipsPerVideo = 10, quantity = 1, splitterFile = null, useSplitter = false } = req.body;

        if (!files || files.length === 0) {
            return res.status(400).json({ error: 'No video files provided' });
        }

        if (quantity < 1) {
            return res.status(400).json({ error: 'Quantity must be at least 1' });
        }

        if (clipsPerVideo < 2 || clipsPerVideo > 50) {
            return res.status(400).json({ error: 'Clips per video must be between 2 and 50' });
        }

        // Validate splitter file if it's being used
        if (useSplitter && !splitterFile) {
            return res.status(400).json({ error: 'Splitter video is required when useSplitter is enabled' });
        }

        // Set up Server-Sent Events
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        });

        const sendProgress = (data) => {
            const message = `data: ${JSON.stringify(data)}\n\n`;
            console.log('Sending SSE:', data);
            res.write(message);
        };

        // Generate videos
        for (let i = 0; i < quantity; i++) {
            try {
                sendProgress({
                    type: 'video-start',
                    videoNumber: i + 1,
                    totalVideos: quantity
                });

                const video = await generateSingleVideo(
                    files,
                    clipsPerVideo,
                    i + 1,
                    (progress, message) => {
                        sendProgress({
                            type: 'progress',
                            videoNumber: i + 1,
                            progress,
                            message
                        });
                    },
                    useSplitter ? splitterFile : null
                );

                sendProgress({
                    type: 'video-ready',
                    videoNumber: i + 1,
                    video
                });

            } catch (error) {
                sendProgress({
                    type: 'error',
                    videoNumber: i + 1,
                    error: error.message
                });
            }
        }

        sendProgress({ type: 'complete' });
        res.end();

    } catch (error) {
        console.error('Generate videos error:', error);
        res.write(`data: ${JSON.stringify({
            type: 'error',
            error: error.message
        })}\n\n`);
        res.end();
    }
});

// Download video endpoint
router.get('/download/:filename', async (req, res) => {
    try {
        const { filename } = req.params;
        const filePath = path.join('output', filename);

        // Verify file exists
        await fs.access(filePath);

        res.download(filePath, filename, (error) => {
            if (error) {
                console.error('Download error:', error);
                res.status(500).json({ error: 'Failed to download file' });
            }
        });
    } catch (error) {
        console.error('File not found:', error);
        res.status(404).json({ error: 'File not found' });
    }
});

// Cleanup endpoint
router.delete('/cleanup', async (req, res) => {
    try {
        const directories = ['uploads', 'output', 'temp'];
        let cleanedFiles = 0;

        for (const dir of directories) {
            try {
                // Check if directory exists before trying to read it
                await fs.access(dir);
                const files = await fs.readdir(dir);
                for (const file of files) {
                    const filePath = path.join(dir, file);
                    try {
                        await fs.unlink(filePath);
                        cleanedFiles++;
                        console.log(`Cleaned up: ${filePath}`);
                    } catch (unlinkError) {
                        console.error(`Failed to delete ${filePath}:`, unlinkError);
                    }
                }
            } catch (error) {
                if (error.code === 'ENOENT') {
                    console.log(`Directory ${dir} does not exist, skipping cleanup`);
                } else {
                    console.error(`Error cleaning ${dir}:`, error);
                }
            }
        }

        res.json({
            success: true,
            message: `Cleaned up ${cleanedFiles} files`
        });
    } catch (error) {
        console.error('Cleanup error:', error);
        res.status(500).json({ error: 'Failed to cleanup files' });
    }
});

module.exports = router;
